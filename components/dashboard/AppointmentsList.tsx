"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Calendar, Clock, MapPin, Trash2 } from 'lucide-react'
import { format, isAfter, parseISO } from 'date-fns'
import { de } from 'date-fns/locale'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { toast } from "sonner"

interface Appointment {
  id: number
  title: string
  startsAt: string
  endsAt: string
  canceledWhy?: string | null
  services?: Array<{
    id: number
    name: string
  }>
}

interface AppointmentsListProps {
  initialAppointments: Appointment[]
}

/**
 * Client component for displaying and managing appointments
 */
export default function AppointmentsList({ initialAppointments }: AppointmentsListProps) {
  const [appointments, setAppointments] = useState<Appointment[]>(initialAppointments)
  const [cancellingId, setCancellingId] = useState<number | null>(null)

  // Filter and sort appointments
  const upcomingAppointments = appointments
    .filter(appointment => {
      // Filter out cancelled appointments
      if (appointment.canceledWhy) return false
      
      // Filter out past appointments
      const appointmentDate = parseISO(appointment.startsAt)
      return isAfter(appointmentDate, new Date())
    })
    .sort((a, b) => parseISO(a.startsAt).getTime() - parseISO(b.startsAt).getTime())

  const handleCancelAppointment = async (appointmentId: number) => {
    setCancellingId(appointmentId)
    
    try {
      const response = await fetch(`/api/booking/appointments/${appointmentId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: 'cancelled by user' }),
      })

      if (!response.ok) {
        throw new Error('Failed to cancel appointment')
      }

      // Remove the cancelled appointment from the list
      setAppointments(prev => prev.filter(apt => apt.id !== appointmentId))
      
      toast.success('Appointment cancelled successfully')
    } catch (error) {
      console.error('Error cancelling appointment:', error)
      toast.error('Failed to cancel appointment. Please try again.')
    } finally {
      setCancellingId(null)
    }
  }

  const getStatusColor = (appointment: Appointment) => {
    // You can customize this based on your appointment status logic
    const appointmentDate = parseISO(appointment.startsAt)
    const now = new Date()
    const hoursUntil = (appointmentDate.getTime() - now.getTime()) / (1000 * 60 * 60)
    
    if (hoursUntil < 24) {
      return 'bg-orange-100 text-orange-800' // Soon
    }
    return 'bg-green-100 text-green-800' // Confirmed
  }

  const getStatusText = (appointment: Appointment) => {
    const appointmentDate = parseISO(appointment.startsAt)
    const now = new Date()
    const hoursUntil = (appointmentDate.getTime() - now.getTime()) / (1000 * 60 * 60)
    
    if (hoursUntil < 24) {
      return 'Soon'
    }
    return 'Confirmed'
  }

  if (upcomingAppointments.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No appointments yet</h3>
          <p className="text-muted-foreground text-center mb-4">
            You haven&apos;t booked any appointments yet. Get started by booking your first appointment.
          </p>
          <Button asChild>
            <Link href="/">
              Book Your First Appointment
            </Link>
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid gap-4">
      {upcomingAppointments.map((appointment) => {
        const appointmentDate = parseISO(appointment.startsAt)
        const serviceName = appointment.services && appointment.services.length > 0 
          ? appointment.services[0].name 
          : appointment.title || 'Appointment'

        return (
          <Card key={appointment.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{serviceName}</CardTitle>
                  <CardDescription className="flex items-center gap-4 mt-2">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {format(appointmentDate, 'EEEE, dd.MM.yyyy', { locale: de })}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {format(appointmentDate, 'HH:mm', { locale: de })} Uhr
                    </span>
                    <span className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      Main Clinic
                    </span>
                  </CardDescription>
                </div>
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment)}`}>
                  {getStatusText(appointment)}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" disabled>
                  Reschedule
                </Button>
                
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      disabled={cancellingId === appointment.id}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      {cancellingId === appointment.id ? 'Cancelling...' : 'Cancel'}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Cancel Appointment</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to cancel your appointment for {serviceName} on{' '}
                        {format(appointmentDate, 'EEEE, dd.MM.yyyy', { locale: de })} at{' '}
                        {format(appointmentDate, 'HH:mm', { locale: de })} Uhr?
                        <br /><br />
                        This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Keep Appointment</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleCancelAppointment(appointment.id)}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Cancel Appointment
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
