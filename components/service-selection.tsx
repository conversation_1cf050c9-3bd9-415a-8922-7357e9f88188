"use client"

import { useEffect, useState, useMemo } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Clock } from "lucide-react"
import { useAppDispatch, useAppSelector } from "@/store"
import { nextStep, setServices } from "@/store/appointmentBookingSlice"
import { fetchBookingServices } from "@/store/bookingSlice"
import type { Service as BookingService } from "@/types/booking"
import type { Service as AppointmentService } from "@/store/appointmentBookingSlice"

export function ServiceSelection() {
  const dispatch = useAppDispatch()
  const { categories, isLoading: loading, hasLoadedServices } = useAppSelector((state) => state.booking)
  const { services: selectedServices } = useAppSelector((state) => state.appointmentBooking.appointmentData)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("alle")

  useEffect(() => {
    // Only fetch services if we haven't loaded them yet and not currently loading
    if (!hasLoadedServices && !loading) {
      dispatch(fetchBookingServices())
    }
  }, [dispatch, hasLoadedServices, loading])

  // Helper function to convert BookingService to AppointmentService
  const convertToAppointmentService = (service: BookingService): AppointmentService => ({
    id: service.id.toString(),
    name: service.displayName || service.name,
    externalName: service.name,
    duration: service.duration,
    price: typeof service.price === 'number' ? (service.price / 100).toFixed(2) : (service.price as string),
    gross: typeof service.price === 'number' ? service.price / 100 : parseFloat(service.price as string),
    selected: true
  })

  const toggleService = (service: BookingService) => {
    const isSelected = selectedServices.some((s) => s.id === service.id.toString())
    let newSelectedServices: AppointmentService[]

    if (isSelected) {
      newSelectedServices = selectedServices.filter((s) => s.id !== service.id.toString())
    } else {
      newSelectedServices = [...selectedServices, convertToAppointmentService(service)]
    }

    dispatch(setServices(newSelectedServices))
  }

  const getTotalDuration = () => {
    return selectedServices.reduce((total, service) => total + service.duration, 0)
  }

  const getTotalPrice = () => {
    return selectedServices.reduce((total, service) => total + Number.parseFloat(service.price), 0)
  }

  const handleNext = () => {
    if (selectedServices.length > 0) {
      dispatch(nextStep())
    }
  }

  // Frontend search and filtering
  const filteredServices = useMemo(() => {
    let allServices: BookingService[] = []

    if (selectedCategoryId === "alle") {
      // Show all services from all categories
      allServices = categories.flatMap((category) => category.services)
    } else {
      // Show services from selected category
      const selectedCategory = categories.find((category) => category.id.toString() === selectedCategoryId)
      if (selectedCategory) {
        allServices = selectedCategory.services
      }
    }

    // Apply search filter
    if (searchTerm.trim()) {
      allServices = allServices.filter(
        (service) =>
          (service.displayName || service.name).toLowerCase().includes(searchTerm.toLowerCase()) ||
          service.name.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    return allServices
  }, [categories, selectedCategoryId, searchTerm])

  if (loading) {
    return (
      <div className="w-full max-w-4xl mx-auto h-full">
        <Card className="w-full h-full flex items-center justify-center">
          <div>Loading services...</div>
        </Card>
      </div>
    )
  }

  return (
    <div className="w-full max-w-4xl mx-auto h-full">
      <Card className="w-full h-full flex flex-col">
        <CardContent className="p-6 flex-1 flex flex-col">
          <div className="flex justify-between items-center mb-6">
            <Input
              placeholder="Suche nach Services"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
            <Button onClick={handleNext} disabled={selectedServices.length === 0}>
              Weiter
            </Button>
          </div>

          {/* Categories - Vertically scrollable tabs */}
          <div className="flex flex-wrap gap-2 mb-6 border-b pb-4 max-h-32 overflow-y-scroll w-auto">
            {/* Add "Alle" category */}
            <button
              onClick={() => setSelectedCategoryId("alle")}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                selectedCategoryId === "alle"
                  ? "border-gray-900 text-gray-900"
                  : "border-transparent text-gray-500 hover:text-gray-700"
              }`}
            >
              Alle
            </button>
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategoryId(category.id.toString())}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                  selectedCategoryId === category.id.toString()
                    ? "border-gray-900 text-gray-900"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                {category.displayName || category.name}
              </button>
            ))}
          </div>

          {/* Services - Horizontally scrollable grid */}
          <div className="mb-6 overflow-x-auto flex-1">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-2 min-w-max max-h-[450px] overflow-auto" style={{ minWidth: "800px" }}>
              {filteredServices.map((service) => {
                const isSelected = selectedServices.some((s) => s.id === service.id)
                return (
                  <div
                    key={service.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors bg-white ${
                      isSelected ? "border-green-500 bg-green-50" : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => toggleService(service)}
                  >
                    <div className="flex gap-4 min-h-32">
                      <div className="w-32 h-32 flex-shrink-0">
                        <Image
                          src={service.image || "/placeholder.svg?height=128&width=128"}
                          alt={service.displayName || service.name}
                          width={128}
                          height={128}
                          className="w-full h-full object-cover rounded"
                        />
                      </div>
                      <div className="flex-1 flex flex-col">
                        <div className="flex-1">
                          <h3 className="font-semibold text-base mb-2">{service.displayName || service.name}</h3>
                          {service.displayDescription && (
                            <p className="text-gray-600 text-sm leading-relaxed">{service.displayDescription}</p>
                          )}
                        </div>
                        <div className="flex items-center justify-between mt-auto pt-2">
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <Clock className="w-4 h-4" />
                            <span>{service.duration}Min.</span>
                            <span className="ml-2">ab €{(typeof service.price === 'number' ? service.price / 100 : parseFloat(service.price as string)).toFixed(2)}</span>
                          </div>
                          <Checkbox checked={isSelected} onChange={() => toggleService(service)} className="ml-4" />
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {selectedServices.length > 0 && (
            <div className="border-t pt-4">
              <div className="flex justify-between items-center">
                <div>
                  <div className="font-medium">Dauer: {getTotalDuration()} Min.</div>
                  <div className="font-medium">Gesamt: ab € {getTotalPrice().toFixed(2)}</div>
                </div>
                <Button onClick={handleNext}>Weiter</Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
