import qs from 'qs';
import { getEnv } from './ctx';

export const ccRequest = async <T>(
    url: string,
    options:
        | 'GET'
        | 'POST'
        | 'DELETE'
        | 'PUT'
        | {
            method: 'GET' | 'POST' | 'DELETE' | 'PUT';
            headers?: { [key: string]: string };
            body?: Record<string, unknown>;
            params?: { [key: string]: string | number | boolean | null };
        },
): Promise<T> => {
    const env = await getEnv();
    try {
        if (!env.CC_API_TOKEN)
            throw new Error('CC Token not found in the environment');

        const requestOptions: {
            method: string;
            body?: string;
            headers?: Record<string, string>;
        } = {
            method: 'GET',
            headers: {
                Authorization: 'Bearer ' + env.CC_API_TOKEN,
                'Accept-Encoding': 'application/json',
                'Content-Type': 'application/json',
            },
        };
        let reqUrl = env.CC_API_URL + url;
        if (typeof options === 'string') {
            requestOptions.method = options;
        } else {
            requestOptions.method = options.method;
            if (options.body) requestOptions['body'] = JSON.stringify(options.body);
            if (options.headers)
                requestOptions['headers'] = {
                    ...requestOptions.headers,
                    ...options.headers,
                };
            if (options.params) reqUrl += '?' + qs.stringify(options.params);
        }
        const res = await fetch(reqUrl, requestOptions);
        const response = await res.json();
        if (!res.ok) {
            throw response
        }
        return response as T;
    } catch (error) {
        console.log('====================================');
        console.log('Request Error: (In request file) -> ', error);
        console.log('====================================');
        throw error;
    }
};
