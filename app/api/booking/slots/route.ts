import { NextRequest, NextResponse } from "next/server";
import { fetchSlotsFromCC } from "@/lib/clinicore";
import { CCSlot } from "@/types/booking";

/**
 * Group appointments by date
 */
function groupAppointmentsByDate(appointments: CCSlot[]): Record<string, CCSlot[]> {
  const groupedAppointments: Record<string, CCSlot[]> = {};

  appointments.forEach((appointment) => {
    const date = appointment.date.split('T')[0]; // Extract the date portion
    if (!groupedAppointments[date]) {
      groupedAppointments[date] = [];
    }
    groupedAppointments[date].push(appointment);
  });

  return groupedAppointments;
}

/**
 * GET handler for fetching available time slots
 */
export async function GET(req: NextRequest) {
  try {
    const queryParams = req.nextUrl.searchParams;
    const services = queryParams.get('services'); // comma-separated service IDs
    const duration = queryParams.get('duration');
    const user = queryParams.get('user');
    const start = queryParams.get('start');
    const end = queryParams.get('end');

    // Parse service IDs
    const serviceIds = services ? services.split(',').map(id => parseInt(id)) : [];
    
    if (serviceIds.length === 0) {
      return NextResponse.json({
        status: 400,
        message: "Mindestens ein Service muss angegeben werden",
      }, { status: 400 });
    }

    // For multiple services, we'll fetch slots for the first service
    // In a real implementation, you might want to find common available slots
    const primaryServiceId = serviceIds[0];

    const params: Record<string, string | number> = {
      service: primaryServiceId,
    };

    if (duration) {
      params.duration = parseInt(duration);
    }

    if (user) {
      params.user = parseInt(user);
    }

    if (start) {
      params.start = start;
    }

    if (end) {
      params.end = end;
    }

    const slots = await fetchSlotsFromCC(params);
    const groupedSlots = groupAppointmentsByDate(slots);

    return NextResponse.json({
      status: 200,
      message: "OK",
      data: groupedSlots,
    });
  } catch (error) {
    console.error('Error fetching slots:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({
      status: 500,
      message: "Fehler beim Laden der verfügbaren Termine. Bitte versuchen Sie es später noch einmal.",
      error: errorMessage,
    }, { status: 500 });
  }
}
