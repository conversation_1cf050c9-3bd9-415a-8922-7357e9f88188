import { NextRequest, NextResponse } from "next/server";
import {
  createOrUpdatePatientInCC,
  createAppointmentInCC
} from "@/lib/clinicore";
import { getDB } from "@/database";
import { appointments, patients } from "@/database/schema";
import { eq } from "drizzle-orm";

interface BookingRequestBody {
  services: number[];
  slot: string;
  duration?: number;
  user?: number | null;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  message?: string;
  birthday?: string;
}

/**
 * POST handler for creating a booking
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as BookingRequestBody;
    const {
      services,
      slot,
      duration = 30,
      user = null,
      firstName,
      lastName,
      email,
      phone,
      message,
      birthday
    } = body;

    // Validate required fields
    if (!services || !Array.isArray(services) || services.length === 0) {
      return NextResponse.json({
        status: 400,
        message: "Services sind erforderlich",
      }, { status: 400 });
    }

    if (!slot) {
      return NextResponse.json({
        status: 400,
        message: "Termin ist erforderlich",
      }, { status: 400 });
    }

    if (!email && !phone) {
      return NextResponse.json({
        status: 400,
        message: "E-Mail oder Telefonnummer ist erforderlich",
      }, { status: 400 });
    }

    // Create or update patient in CliniCore
    const patientData = {
      firstName,
      lastName,
      email,
      phone,
      birthDay: birthday,
      message,
    };

    const ccPatient = await createOrUpdatePatientInCC(patientData);
    
    if (!ccPatient) {
      throw new Error("Fehler beim Erstellen des Patientenprofils");
    }

    // Create appointment in CliniCore
    const appointmentSlot = new Date(slot);
    const appointmentDuration = duration * 60; // Convert minutes to seconds
    
    const ccAppointment = await createAppointmentInCC({
      slot: appointmentSlot,
      duration: appointmentDuration,
      patient: ccPatient,
      services: services,
      user: user ?? undefined,
      message: message,
    });

    if (!ccAppointment) {
      throw new Error("Fehler beim Erstellen des Termins");
    }

    // Store patient in local database if not exists
    let localPatient;
    try {
      const db = await getDB();
      const existingPatient = await db
        .select()
        .from(patients)
        .where(eq(patients.ccID, ccPatient.id))
        .limit(1);

      if (existingPatient.length > 0) {
        localPatient = existingPatient[0];

        // Update patient data
        await db
          .update(patients)
          .set({
            firstName: ccPatient.firstName,
            lastName: ccPatient.lastName,
            email: ccPatient.email,
            phone: ccPatient.phoneMobile,
            updatedAt: new Date(),
          })
          .where(eq(patients.id, localPatient.id));
      } else {
        // Create new patient
        const newPatients = await db
          .insert(patients)
          .values({
            ccID: ccPatient.id,
            firstName: ccPatient.firstName,
            lastName: ccPatient.lastName,
            email: ccPatient.email,
            phone: ccPatient.phoneMobile,
          })
          .returning();

        localPatient = newPatients[0];
      }
    } catch (dbError) {
      console.error('Database error (patient):', dbError);
      // Continue even if local DB fails
    }

    // Store appointment in local database
    try {
      if (localPatient) {
        const db = await getDB();
        await db.insert(appointments).values({
          ccAppointmentId: ccAppointment.id,
          patientId: localPatient.id,
          serviceIds: services,
          slotDate: appointmentSlot,
          duration: appointmentDuration,
          status: 'pending',
          notes: message,
        });
      }
    } catch (dbError) {
      console.error('Database error (appointment):', dbError);
      // Continue even if local DB fails
    }

    return NextResponse.json({
      status: 201,
      message: "Termin erfolgreich gebucht",
      appointment: {
        id: ccAppointment.id,
        date: appointmentSlot.toISOString(),
        services: services,
        patient: {
          firstName: ccPatient.firstName,
          lastName: ccPatient.lastName,
          email: ccPatient.email,
          phone: ccPatient.phoneMobile,
        },
      },
    });
  } catch (error) {
    console.error('Booking error:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({
      status: 500,
      message: errorMessage || "Fehler beim Buchen des Termins. Bitte versuchen Sie es erneut.",
      error: errorMessage,
    }, { status: 500 });
  }
}
