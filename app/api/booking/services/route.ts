import { NextResponse } from "next/server";
import { getDB } from "@/database";
import { services, productGroups } from "@/database/schema";
import { eq, and, asc } from "drizzle-orm";

/**
 * Service interface matching the expected response format
 */
interface Service {
  id: string;
  name: string;
  duration: number;
  price: string;
  gross: number;
  externalName: string;
  ccID: number;
  image?: string;
}

/**
 * Product group interface for the response
 */
interface ProductGroup {
  id: string;
  name: string;
  services: Service[];
  displayOrder: number;
}

/**
 * Service with display order for internal sorting
 */
interface ServiceWithOrder extends Service {
  displayOrder: number;
}

/**
 * GET handler for fetching booking services from local database
 * Returns services grouped by categories, respecting the hidden field
 * Matches the old API response format for frontend compatibility
 */
export async function GET() {
  try {
    const db = await getDB();

    // Fetch all non-hidden product groups with their services
    const groupsWithServices = await db
      .select({
        groupId: productGroups.id,
        groupName: productGroups.name,
        groupDisplayName: productGroups.displayName,
        groupDisplayOrder: productGroups.displayOrder,
        serviceId: services.id,
        serviceName: services.name,
        serviceCcId: services.ccID,
        serviceDuration: services.duration,
        servicePrice: services.price,
        serviceDisplayName: services.displayName,
        serviceDescription: services.description,
        serviceDisplayDescription: services.displayDescription,
        serviceDisplayOrder: services.displayOrder,
        serviceImage: services.image,
      })
      .from(productGroups)
      .leftJoin(
        services,
        and(
          eq(services.groupId, productGroups.id),
          eq(services.hidden, false) // Only include non-hidden services
        )
      )
      .where(eq(productGroups.hidden, false)) // Only include non-hidden groups
      .orderBy(asc(productGroups.displayOrder), asc(productGroups.name));

    // Group the results by product group
    const groupMap: Record<string, ProductGroup & { servicesWithOrder: ServiceWithOrder[] }> = {};

    groupsWithServices.forEach((row) => {
      const groupId = row.groupId;
      const groupName = row.groupDisplayName || row.groupName || 'Unnamed Group';

      if (!groupMap[groupId]) {
        groupMap[groupId] = {
          id: groupId,
          name: groupName,
          services: [],
          displayOrder: row.groupDisplayOrder || 0,
          servicesWithOrder: []
        };
      }

      // Add service if it exists (leftJoin might return null services)
      if (row.serviceId && row.serviceCcId && row.serviceName && row.serviceDuration !== null && row.servicePrice !== null) {
        const serviceWithOrder: ServiceWithOrder = {
          id: row.serviceCcId.toString(), // Use CC ID for frontend compatibility
          name: row.serviceDisplayName || row.serviceName,
          duration: Math.round(row.serviceDuration / 60), // Convert seconds to minutes
          price: (row.servicePrice / 100).toFixed(2), // Convert cents to euros
          gross: row.servicePrice / 100, // Convert cents to euros
          externalName: row.serviceName,
          ccID: row.serviceCcId,
          image: row.serviceImage || undefined, // Convert null to undefined
          displayOrder: row.serviceDisplayOrder || 0
        };

        groupMap[groupId].servicesWithOrder.push(serviceWithOrder);
      }
    });

    // Sort services within each group by displayOrder and convert to final format
    const data = Object.values(groupMap)
      .filter(group => group.servicesWithOrder.length > 0)
      .map(group => {
        // Sort services by displayOrder, then by name
        const sortedServices = group.servicesWithOrder
          .sort((a, b) => {
            if (a.displayOrder !== b.displayOrder) {
              return a.displayOrder - b.displayOrder;
            }
            return a.name.localeCompare(b.name);
          })
          .map(({ displayOrder, ...service }) => service); // Remove displayOrder from final output

        return {
          id: group.id,
          name: group.name,
          services: sortedServices
        };
      })
      .sort((a, b) => {
        // Groups are already sorted by the database query, but ensure consistency
        const groupA = groupMap[a.id];
        const groupB = groupMap[b.id];
        if (groupA.displayOrder !== groupB.displayOrder) {
          return groupA.displayOrder - groupB.displayOrder;
        }
        return a.name.localeCompare(b.name);
      });

    return NextResponse.json({
      status: 200,
      message: "OK",
      data: data
    });
  } catch (error) {
    console.error('Error fetching booking services from database:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({
      status: 500,
      message: "Fehler beim Laden der Services. Bitte versuchen Sie es später noch einmal.",
      error: errorMessage,
    }, { status: 500 });
  }
}
