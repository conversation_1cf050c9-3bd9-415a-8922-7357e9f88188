import { NextRequest, NextResponse } from "next/server";
import { searchPatientInCC, ccRequest } from "@/lib/clinicore";

/**
 * Fetch appointments by IDs from CliniCore
 */
async function fetchAppointmentsByIds(ids: number[]): Promise<unknown[]> {
  const appointmentPromises = ids.map((id) => fetchAppointmentById(id));
  const appointments = await Promise.all(appointmentPromises);
  return appointments.filter(apt => apt !== null); // Filter out failed requests
}

/**
 * Fetch single appointment by ID from CliniCore
 */
async function fetchAppointmentById(id: number): Promise<unknown | null> {
  try {
    const appointment = await ccRequest(`/appointments/${id}`, 'GET');
    return appointment;
  } catch (error) {
    console.error(`Failed to fetch appointment ${id}:`, error);
    return null;
  }
}

/**
 * GET handler for fetching user appointments
 */
export async function GET(req: NextRequest) {
  try {
    const queryParams = req.nextUrl.searchParams;
    const email = queryParams.get("email");
    const phone = queryParams.get("phone");

    if (!email && !phone) {
      return NextResponse.json({
        status: 400,
        message: "E-Mail oder Telefonnummer ist erforderlich",
      }, { status: 400 });
    }

    // Search for patient in CliniCore
    const patient = await searchPatientInCC({ email, phone });
    
    if (!patient) {
      return NextResponse.json({
        status: 404,
        message: "Keine Termine gefunden",
      }, { status: 404 });
    }

    // Fetch appointments for this patient
    let appointments: unknown[] = [];
    if (patient.appointments && patient.appointments.length > 0) {
      appointments = await fetchAppointmentsByIds(patient.appointments);
    }

    return NextResponse.json({
      appointments,
      patient,
    });
  } catch (error) {
    console.error('Error fetching appointments:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({
      status: 500,
      message: "Fehler beim Laden der Termine. Bitte versuchen Sie es später noch einmal.",
      error: errorMessage,
    }, { status: 500 });
  }
}
