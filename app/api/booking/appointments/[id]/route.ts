import { NextRequest, NextResponse } from "next/server";
import { ccRequest } from "@/lib/clinicore";

/**
 * Interface for the request body when cancelling an appointment
 */
interface CancelAppointmentRequestBody {
  reason?: string;
}

/**
 * Interface for CliniCore appointment cancellation payload
 */
interface CancelAppointmentPayload {
  canceledWhy: string;
}

/**
 * Interface for CliniCore API response when cancelling appointments
 */
interface CancelAppointmentResponse {
  appointment?: {
    id: number;
    status: string;
    canceledWhy?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

/**
 * Cancel an appointment in CliniCore
 */
async function cancelAppointmentInCC(id: number, reason: string): Promise<CancelAppointmentResponse> {
  try {
    const payload: CancelAppointmentPayload = {
      canceledWhy: reason,
    };
    
    const response = await ccRequest(`/appointments/${id}`, {
      method: "PUT",
      body: { appointment: payload },
    });
    
    return response as CancelAppointmentResponse;
  } catch (error) {
    console.error(`Failed to cancel appointment ${id}:`, error);
    throw error;
  }
}

/**
 * Validate appointment ID parameter
 */
function validateAppointmentId(id: string): number {
  const appointmentId = parseInt(id, 10);
  
  if (isNaN(appointmentId) || appointmentId <= 0) {
    throw new Error("Invalid appointment ID");
  }
  
  return appointmentId;
}

/**
 * Parse and validate request body for cancellation reason
 */
async function parseRequestBody(req: NextRequest): Promise<string> {
  const defaultReason = "cancelled by user";
  
  try {
    const body = await req.json() as CancelAppointmentRequestBody;
    
    // Validate and sanitize the reason if provided
    if (body.reason && typeof body.reason === 'string') {
      const sanitizedReason = body.reason.trim();
      return sanitizedReason.length > 0 ? sanitizedReason : defaultReason;
    }
    
    return defaultReason;
  } catch {
    // If JSON parsing fails or body is empty, use default reason
    console.log('No valid JSON body provided, using default cancellation reason');
    return defaultReason;
  }
}

/**
 * DELETE handler for cancelling appointments
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params since it's now a Promise in Next.js 15
    const resolvedParams = await params;

    // Validate appointment ID
    const appointmentId = validateAppointmentId(resolvedParams.id);

    // Parse request body for cancellation reason
    const reason = await parseRequestBody(req);

    // Cancel appointment in CliniCore
    const cancelledAppointment = await cancelAppointmentInCC(appointmentId, reason);

    return NextResponse.json({
      status: 200,
      message: "Termin erfolgreich storniert",
      appointment: cancelledAppointment.appointment,
    });
  } catch (error: unknown) {
    console.error('Error cancelling appointment:', error);
    
    // Handle different types of errors appropriately
    if (error instanceof Error) {
      // Handle validation errors
      if (error.message === "Invalid appointment ID") {
        return NextResponse.json({
          status: 400,
          message: "Ungültige Termin-ID",
          error: error.message,
        }, { status: 400 });
      }
      
      // Handle CliniCore API errors
      if (error.message.includes('CliniCore')) {
        return NextResponse.json({
          status: 502,
          message: "Fehler beim Verbinden mit dem Buchungssystem. Bitte versuchen Sie es später noch einmal.",
          error: error.message,
        }, { status: 502 });
      }
    }
    
    // Generic server error
    return NextResponse.json({
      status: 500,
      message: "Fehler beim Stornieren des Termins. Bitte versuchen Sie es später noch einmal.",
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
