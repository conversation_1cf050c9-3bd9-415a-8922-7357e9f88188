import { getAuth } from '@/lib/auth'
import { headers } from 'next/headers'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import RecentAppointments from '@/components/dashboard/RecentAppointments'
import PastAppointments from '@/components/dashboard/PastAppointments'
import RecentAppointmentsLoading from '@/components/dashboard/RecentAppointmentsLoading'
import { searchPatientInCC } from '@/lib/clinicore'
import { Suspense } from 'react'

interface DashboardAppointment {
  id: number
  title: string
  startsAt: string
  endsAt: string
  canceledWhy?: string | null
  services?: Array<{
    id: number
    name: string
  }>
}

async function fetchUserAppointments(email?: string, phone?: string): Promise<DashboardAppointment[]> {
    try {
        if (!email && !phone) {
            return []
        }

        // Search for patient in CliniCore
        const patient = await searchPatientInCC({ email, phone })

        if (!patient || !patient.appointments || patient.appointments.length === 0) {
            return []
        }

        // Fetch appointments by IDs
        const appointmentPromises = patient.appointments.map(async (id: number) => {
            try {
                const response = await fetch(`${process.env.CC_API_URL}/appointments/${id}`, {
                    headers: {
                        'Authorization': `Bearer ${process.env.CC_API_TOKEN}`,
                        'Content-Type': 'application/json',
                    },
                })

                if (!response.ok) {
                    return null
                }

                const data = await response.json() as { appointment?: DashboardAppointment } | DashboardAppointment
                return data && typeof data === 'object' && 'appointment' in data ? data.appointment : data as DashboardAppointment
            } catch (error) {
                console.error(`Failed to fetch appointment ${id}:`, error)
                return null
            }
        })

        const appointments = await Promise.all(appointmentPromises)
        return appointments.filter((apt): apt is DashboardAppointment => apt !== null)
    } catch (error) {
        console.error('Error fetching user appointments:', error)
        return []
    }
}

export default async function UserDashboard() {
    const session = await (await getAuth()).api.getSession({
        headers: await headers(),
    })

    const user = session?.user

    // Fetch user appointments
    const appointments = await fetchUserAppointments(user?.email, undefined)

    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
                <p className="text-muted-foreground">
                    Welcome back, {user?.name || user?.email}!
                </p>
            </div>

            {/* Appointments Section */}
            <div className="grid gap-6 lg:grid-cols-3">
                <div className="lg:col-span-2 space-y-6">
                    {/* Upcoming Appointments */}
                    <Suspense fallback={<RecentAppointmentsLoading />}>
                        <RecentAppointments appointments={appointments} />
                    </Suspense>

                    {/* Past Appointments */}
                    <Suspense fallback={<RecentAppointmentsLoading />}>
                        <PastAppointments appointments={appointments} />
                    </Suspense>
                </div>
                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>
                                Common tasks and shortcuts
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <Button asChild className="w-full">
                                <Link href="/">
                                    Book New Appointment
                                </Link>
                            </Button>
                            <Button asChild variant="outline" className="w-full">
                                <Link href="/dashboard/profile">
                                    Edit Profile
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </div>



            {user?.role === 'admin' && (
                <Card className="border-orange-200 bg-orange-50">
                    <CardHeader>
                        <CardTitle className="text-orange-800">Admin Access</CardTitle>
                        <CardDescription className="text-orange-700">
                            You have administrator privileges
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Button asChild className="bg-orange-600 hover:bg-orange-700">
                            <Link href="/~">
                                Go to Admin Dashboard
                            </Link>
                        </Button>
                    </CardContent>
                </Card>
            )}
        </div>
    )
}
